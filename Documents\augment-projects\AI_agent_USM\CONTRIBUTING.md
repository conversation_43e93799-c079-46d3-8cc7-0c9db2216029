# 🤝 貢獻指南

> 歡迎加入 AI 社群 Agent 專案！這份指南將幫助你了解如何參與專案開發。

## 🎯 貢獻方式

### 🐛 回報問題
- 使用 [GitHub Issues](https://github.com/your-org/ai-social-agent/issues) 回報 Bug
- 提供詳細的問題描述和重現步驟
- 包含系統環境資訊

### 💡 功能建議
- 在 Issues 中標記為 `enhancement`
- 說明功能的使用場景和預期效果
- 參考使用者故事地圖的格式

### 📝 文件改進
- 修正錯字或不清楚的說明
- 增加範例和使用案例
- 翻譯文件到其他語言

### 💻 程式碼貢獻
- 修復 Bug
- 實作新功能
- 改善效能
- 增加測試覆蓋率

## 🚀 開發流程

### 1️⃣ 環境設置
```bash
# 1. Fork 並 Clone 專案
git clone https://github.com/your-username/ai-social-agent.git
cd ai-social-agent

# 2. 建立虛擬環境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 3. 安裝依賴
pip install -r requirements.txt
cd frontend && npm install

# 4. 設定環境變數
cp .env.example .env
# 編輯 .env 檔案
```

### 2️⃣ 分支策略
```bash
# 從 main 分支建立功能分支
git checkout main
git pull origin main
git checkout -b feature/your-feature-name

# 或修復分支
git checkout -b fix/issue-description
```

### 3️⃣ 開發規範

#### 📏 程式碼風格
- **Python**: 遵循 PEP 8，使用 `black` 格式化
- **JavaScript**: 使用 ESLint + Prettier
- **提交訊息**: 遵循 Conventional Commits

#### 🧪 測試要求
- 新功能必須包含測試
- 測試覆蓋率不得低於 80%
- 所有測試必須通過

#### 📝 文件要求
- 新功能需要更新相關文件
- API 變更需要更新 API 文件
- 重大變更需要更新 README

### 4️⃣ 提交與推送
```bash
# 執行測試
pytest                    # 後端測試
cd frontend && npm test   # 前端測試

# 程式碼格式化
black .                   # Python
cd frontend && npm run format  # JavaScript

# 提交變更
git add .
git commit -m "feat: add new content generation feature"

# 推送到你的 Fork
git push origin feature/your-feature-name
```

### 5️⃣ 建立 Pull Request
1. 在 GitHub 上建立 Pull Request
2. 填寫 PR 模板
3. 等待程式碼審查
4. 根據回饋修改程式碼
5. 合併到主分支

## 📋 Pull Request 檢查清單

### ✅ 程式碼品質
- [ ] 程式碼遵循專案風格指南
- [ ] 沒有 linting 錯誤
- [ ] 變數和函數命名清晰
- [ ] 程式碼有適當的註解

### 🧪 測試
- [ ] 新功能有對應的測試
- [ ] 所有測試都通過
- [ ] 測試覆蓋率符合要求
- [ ] 手動測試功能正常

### 📚 文件
- [ ] 更新相關文件
- [ ] API 變更有文件說明
- [ ] README 需要時已更新
- [ ] 變更日誌已更新

### 🔄 相容性
- [ ] 向後相容性考慮
- [ ] 資料庫遷移（如需要）
- [ ] 環境變數變更說明
- [ ] 部署影響評估

## 🏷️ 提交訊息規範

使用 [Conventional Commits](https://www.conventionalcommits.org/) 格式：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### 類型 (Type)
- `feat`: 新功能
- `fix`: Bug 修復
- `docs`: 文件變更
- `style`: 程式碼格式（不影響功能）
- `refactor`: 重構（不是新功能也不是修復）
- `test`: 增加測試
- `chore`: 建置過程或輔助工具變更

### 範例
```
feat(content): add AI image generation support

- Integrate DALL-E API for image creation
- Add image optimization pipeline
- Update content model to support images

Closes #123
```

## 🔍 程式碼審查指南

### 👀 審查重點
1. **功能正確性**: 程式碼是否實現預期功能
2. **程式碼品質**: 可讀性、可維護性
3. **效能考量**: 是否有效能問題
4. **安全性**: 是否有安全漏洞
5. **測試完整性**: 測試是否充分

### 💬 審查回饋
- 提供建設性的回饋
- 解釋為什麼需要修改
- 提供改進建議
- 認可好的程式碼實踐

## 🏆 貢獻者認可

### 🌟 貢獻類型
- **程式碼**: 實作功能、修復 Bug
- **文件**: 撰寫或改善文件
- **設計**: UI/UX 設計貢獻
- **測試**: 增加測試覆蓋率
- **審查**: 程式碼審查和回饋
- **想法**: 功能建議和架構討論

### 🎖️ 認可方式
- 在 README 中列出貢獻者
- 在發布說明中感謝貢獻者
- 特殊貢獻者獲得維護者權限

## 📞 聯絡方式

### 💬 討論
- [GitHub Discussions](https://github.com/your-org/ai-social-agent/discussions)
- [Discord 社群](https://discord.gg/your-server)

### 📧 聯絡
- 專案維護者: [<EMAIL>](mailto:<EMAIL>)
- 技術問題: 使用 GitHub Issues
- 安全問題: [<EMAIL>](mailto:<EMAIL>)

## 📜 行為準則

我們致力於為所有人提供友善、安全和歡迎的環境。請遵循以下原則：

- 🤝 **尊重**: 尊重不同的觀點和經驗
- 💬 **建設性**: 提供建設性的回饋和批評
- 🌍 **包容**: 歡迎來自不同背景的貢獻者
- 📚 **學習**: 保持開放的學習態度
- 🎯 **專注**: 專注於對專案最有利的事情

違反行為準則的行為將不被容忍，可能導致暫時或永久禁止參與專案。

---

感謝你對 AI 社群 Agent 專案的貢獻！🎉

**📅 指南建立日期**: 2025-01-07  
**🔄 最後更新**: 2025-01-07
